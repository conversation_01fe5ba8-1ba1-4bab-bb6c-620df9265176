<projectSpec>
  <project
        name="fsi_daisy_epwmsync_node"
        device="TMS320F280025C"
        cgtVersion="21.6.0.LTS"
        products="C2000WARE"
        outputFormat="ELF"
        launchWizard="False"
        enableSysConfigTool="true"
        linkerCommandFile=""
        sysConfigBuildOptions="--product ${C2000WARE_ROOT}/.metadata/sdk.json --device F28002x --package 80QFP --part F28002x_80QFP"
        >
    <configuration name="CPU1_RAM" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt -mo --float_support=fpu32 --define=DEBUG --define RAM --diag_warning=225 --diag_suppress=10063"  linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100 "/>
    <configuration name="CPU1_FLASH" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt --float_support=fpu32 --define=DEBUG --define=_FLASH --diag_warning=225 --diag_suppress=10063"  linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100 "/>
    <configuration name="CPU1_LAUNCHXL_RAM" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt -mo --float_support=fpu32 --define=DEBUG --define RAM --diag_warning=225 --diag_suppress=10063 --define=_LAUNCHXL_F280025C" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100  "/>
    <configuration name="CPU1_LAUNCHXL_FLASH" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt --float_support=fpu32 --define=DEBUG --define=_FLASH --diag_warning=225 --diag_suppress=10063 --define=_LAUNCHXL_F280025C" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100  "/>
    
    <configuration name="CPU1_CLB" postBuildStep="mkdir &quot;${BuildDirectory}/simulation&quot;
    ;${CLB_SIM_COMPILER}/g++  -c -DCLB_SIM -I${SYSTEMC_INSTALL}/src -I${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/include/type2 -I${PROJECT_ROOT} -I${CLB_SIM_COMPILER}/include -Og -g -gdwarf-3 -gstrict-dwarf -Wall -MMD -MP -MF${BuildDirectory}/simulation/clb_sim.d -MT${BuildDirectory}/simulation/clb_sim.o -I${BuildDirectory}/syscfg   -fno-threadsafe-statics  -o${BuildDirectory}/simulation/clb_sim.o ${BuildDirectory}/syscfg/clb_sim.cpp
    ;${CLB_SIM_COMPILER}/g++ -DCLB_SIM -Og -g -gdwarf-3 -gstrict-dwarf -Wall -Wl,-Map,${BuildDirectory}/simulation/simulation_output.map -L${SYSTEMC_INSTALL}/build/src -o${BuildDirectory}/simulation/simulation_output.exe ${BuildDirectory}/simulation/clb_sim.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_FSM_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_HLC_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_LUT4_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_OutputLUT_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_counter_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_counter_top_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_FSM_top_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_LUT4_top_SC_model.o ${C2000WARE_ROOT}/utilities/clb_tool/clb_syscfg/systemc/src/type2/CLB_OutputLUT_top_SC_model.o  -Wl,--start-group -lsystemc -Wl,--end-group;./simulation/simulation_output.exe
    ;${NODE_TOOL} &quot;${CLB_SYSCFG_ROOT}/dot_file_libraries/clbDotUtility.js&quot;  &quot;${CLB_SYSCFG_ROOT}&quot; &quot;${BuildDirectory}/syscfg&quot; &quot;${BuildDirectory}/syscfg/clb.dot&quot;"
    compilerBuildOptions="--opt_level=off --opt_for_speed=2 --fp_mode=strict -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${CLB_SYSCFG_ROOT}/systemc/include -v28 -ml -mt --float_support=fpu32 --define=RAM --define=DEBUG -g --diag_warning=225 --diag_suppress=10063 --diag_wrap=off --display_error_number " linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100 -l driverlib.lib -l libc.a -I${C2000WARE_DLIB_ROOT}/ccs/Debug" />

    <pathVariable name="C2000WARE_ROOT" path="../../../../" scope="project" />      
    <pathVariable name="C2000WARE_DLIB_ROOT" path="../../../../driverlib/f28002x/driverlib/" scope="project" />

    <pathVariable name="CLB_SYSCFG_ROOT" path="../../../../utilities/clb_tool/clb_syscfg/" scope="project" />
    <pathVariable name="SYSTEMC_INSTALL" path="../../../../utilities/clb_tool/clb_syscfg/systemc-2.3.3" scope="project" />
    <pathVariable name="CLB_SIM_COMPILER" path="C:/TDM-GCC-64/bin" scope="project" />

    <file action="copy" path="../../../../device_support/f28002x/common/include/driverlib.h" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28002x/common/include/device.h" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28002x/common/source/device.c" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28002x/common/source/f28002x_codestartbranch.asm" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28002x/common/targetConfigs/TMS320F280025C.ccxml" targetDirectory="targetConfigs"  applicableConfigurations="CPU1_LAUNCHXL_RAM, CPU1_LAUNCHXL_FLASH, CPU1_RAM,CPU1_FLASH,CPU1_CLB" />
    <file action="copy" path="../../../../device_support/f28002x/common/cmd/28002x_generic_ram_lnk.cmd" targetDirectory=""  applicableConfigurations="CPU1_RAM, CPU1_LAUNCHXL_RAM, CPU1_CLB" />
    <file action="copy" path="../../../../device_support/f28002x/common/cmd/28002x_generic_flash_lnk.cmd" targetDirectory=""  applicableConfigurations="CPU1_LAUNCHXL_FLASH, CPU1_FLASH" />
    <file action="link" path="../../../../driverlib/f28002x/driverlib/ccs/Debug/driverlib.lib" targetDirectory="" />
    <file action="copy" path="../../../../driverlib/f28002x/driverlib/" targetDirectory="device" excludeFromBuild="True"/>
    
    <file action="copy" path="fsi_daisy_epwmsync_node.c" targetDirectory="" />
    <file action="copy" path="fsi_daisy_epwmsync_node.syscfg" targetDirectory="" />
  </project>
</projectSpec>
