/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "F28004x" --package "F28004x_100PZ" --part "F28004x_100PZ" --product "C2000WARE@**********"
 * @versions {"tool":"1.13.0+2411"}
 */

/**
 * Import the modules used in this configuration.
 */
const clb         = scripting.addModule("/driverlib/clb.js", {}, false);
const clb1        = clb.addInstance();
const clbxbar     = scripting.addModule("/driverlib/clbxbar.js", {}, false);
const clbxbar1    = clbxbar.addInstance();
const dac         = scripting.addModule("/driverlib/dac.js", {}, false);
const dac1        = dac.addInstance();
const epwm        = scripting.addModule("/driverlib/epwm.js", {}, false);
const epwm1       = epwm.addInstance();
const inputxbar   = scripting.addModule("/driverlib/inputxbar.js", {}, false);
const inputxbar1  = inputxbar.addInstance();
const outputxbar  = scripting.addModule("/driverlib/outputxbar.js", {}, false);
const outputxbar1 = outputxbar.addInstance();
const outputxbar2 = outputxbar.addInstance();
const sd          = scripting.addModule("/driverlib/sd.js", {}, false);
const sd1         = sd.addInstance();
const TILE        = scripting.addModule("/utilities/clb_tool/clb_syscfg/source/TILE", {}, false);
const TILE1       = TILE.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
clb1.inputTypeCLB_IN0       = "GLOBAL";
clb1.attachTile             = true;
clb1.$name                  = "myCLB1";
clb1.filterConfigCLB_IN0    = "CLB_FILTER_RISING_EDGE";
clb1.inputsUsed             = ["CLB_IN0","CLB_IN1"];
clb1.globalConfigCLB_IN1    = "CLB_GLOBAL_IN_MUX_CLB_AUXSIG0";
clb1.inputTypeCLB_IN1       = "GLOBAL";
clb1.registerInterrupts     = true;
clb1.synchronizeCLB_IN0     = true;
clb1.clbInt.enableInterrupt = true;

clbxbar1.$name      = "myCLBXBAR0";
clbxbar1.muxesUsed  = ["XBAR_MUX01"];
clbxbar1.mux1Config = "XBAR_CLB_MUX01_INPUTXBAR1";

dac1.shadowValue  = 1024;
dac1.dacBase      = "DACB_BASE";
dac1.enableOutput = true;
dac1.$name        = "myDACB";

epwm1.$name                                                      = "myEPWM0";
epwm1.epwmTimebase_hsClockDiv                                    = "EPWM_HSCLOCK_DIVIDER_1";
epwm1.epwmTimebase_counterMode                                   = "EPWM_COUNTER_MODE_UP_DOWN";
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_A_shadowMode            = true;
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_A_usedEvents            = ["EPWM_AQ_OUTPUT_ON_TIMEBASE_DOWN_CMPA","EPWM_AQ_OUTPUT_ON_TIMEBASE_UP_CMPA"];
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_UP_CMPA   = "EPWM_AQ_OUTPUT_HIGH";
epwm1.epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_DOWN_CMPA = "EPWM_AQ_OUTPUT_LOW";
epwm1.useCase                                                    = "CUSTOM";
epwm1.useInterfacePins                                           = ["EPWM#_A"];
epwm1.epwmTimebase_period                                        = 4;
epwm1.epwmCounterCompare_cmpA                                    = 2;
epwm1.epwm.$assign                                               = "EPWM1";
epwm1.epwm.epwm_aPin.$assign                                     = "GPIO0";

inputxbar1.inputsUsed     = ["inputxbar1Gpio"];
inputxbar1.$name          = "myINPUTXBAR1";
inputxbar1.inputxbar1Gpio = "GPIO24";

outputxbar1.muxesUsed                        = ["XBAR_MUX01"];
outputxbar1.mux1Config                       = "XBAR_OUT_MUX01_CLB1_OUT4";
outputxbar1.$name                            = "myOUTPUTXBAR1";
outputxbar1.outputxbar.$assign               = "OUTPUTXBAR1";
outputxbar1.outputxbar.outputxbarPin.$assign = "GPIO2";

outputxbar2.$name                            = "myOUTPUTXBAR2";
outputxbar2.muxesUsed                        = ["XBAR_MUX03"];
outputxbar2.mux3Config                       = "XBAR_OUT_MUX03_CLB1_OUT5";
outputxbar2.outputxbar.$assign               = "OUTPUTXBAR2";
outputxbar2.outputxbar.outputxbarPin.$assign = "GPIO3";

sd1.Use_FilterChannel_1           = true;
sd1.useCase                       = "CUSTOM";
sd1.useInterfacePins              = ["SD#_C1","SD#_D1"];
sd1.$name                         = "mySDFM1";
sd1.Ch1_DataFilterEnable          = true;
sd1.Ch1_ComparatorEnable          = true;
sd1.Ch1_DOSR                      = 128;
sd1.Ch1_DataFilter_Representation = "SDFM_DATA_FORMAT_16_BIT";
sd1.Ch1_SD_modulatorFrequency     = 5;
sd1.useInterrupts                 = true;
sd1.MFIE                          = ["FILTER1"];
sd1.AE                            = ["FILTER1"];
sd1.registerInterrupts            = ["INT_SDFM","INT_SDFM_DR1"];
sd1.Ch1_HLTZ                      = "0";
sd1.Ch1_HLT                       = 32766;
sd1.sd.$assign                    = "SD1";
sd1.sd.sd_c1Pin.$assign           = "GPIO25";
sd1.sd.sd_d1Pin.$assign           = "GPIO24";

clb1.tile                    = TILE1;
TILE1.$name                  = "TILE1";
TILE1.BOUNDARY.$name         = "BOUNDARY0";
TILE1.LUT_0.$name            = "LUT_0";
TILE1.LUT_0.i0               = "BOUNDARY.in0";
TILE1.LUT_0.i1               = "BOUNDARY.in1";
TILE1.LUT_0.eqn              = "i0 & i1";
TILE1.LUT_1.$name            = "LUT_1";
TILE1.LUT_2.$name            = "LUT_2";
TILE1.FSM_0.$name            = "FSM_0";
TILE1.FSM_1.$name            = "FSM_1";
TILE1.FSM_2.$name            = "FSM_2";
TILE1.COUNTER_0.$name        = "COUNTER_0";
TILE1.COUNTER_0.mode1        = "1";
TILE1.COUNTER_0.mode0        = "LUT_0.OUT";
TILE1.COUNTER_0.reset        = "COUNTER_1.count_match1";
TILE1.COUNTER_1.$name        = "COUNTER_1";
TILE1.COUNTER_1.mode1        = "1";
TILE1.COUNTER_1.reset        = "COUNTER_1.count_match1";
TILE1.COUNTER_1.mode0        = "BOUNDARY.in0";
TILE1.COUNTER_1.match1_val   = "129";
TILE1.COUNTER_1.match2_val   = "128";
TILE1.COUNTER_2.$name        = "COUNTER_2";
TILE1.OUTLUT_0.$name         = "OUTLUT_0";
TILE1.OUTLUT_1.$name         = "OUTLUT_1";
TILE1.OUTLUT_2.$name         = "OUTLUT_2";
TILE1.OUTLUT_3.$name         = "OUTLUT_3";
TILE1.OUTLUT_4.$name         = "OUTLUT_4";
TILE1.OUTLUT_4.eqn           = "i0";
TILE1.OUTLUT_4.i0            = "BOUNDARY.in0";
TILE1.OUTLUT_5.$name         = "OUTLUT_5";
TILE1.OUTLUT_5.i0            = "BOUNDARY.in1";
TILE1.OUTLUT_5.eqn           = "i0";
TILE1.OUTLUT_6.$name         = "OUTLUT_6";
TILE1.OUTLUT_7.$name         = "OUTLUT_7";
TILE1.HLC.$name              = "HLC_0";
TILE1.HLC.e0                 = "COUNTER_1.count_match2";
TILE1.HLC.program0.$name     = "HLCP_0";
TILE1.HLC.program0.instruct0 = "MOV C0, R0";
TILE1.HLC.program0.instruct1 = "MOV C1, R1";
TILE1.HLC.program0.instruct2 = "INTR 1";
TILE1.HLC.program1.$name     = "HLCP_1";
TILE1.HLC.program2.$name     = "HLCP_2";
TILE1.HLC.program3.$name     = "HLCP_3";
TILE1.AOC_0.$name            = "AOC_0";
TILE1.AOC_1.$name            = "AOC_1";
TILE1.AOC_2.$name            = "AOC_2";
TILE1.AOC_3.$name            = "AOC_3";
TILE1.AOC_4.$name            = "AOC_4";
TILE1.AOC_5.$name            = "AOC_5";
TILE1.AOC_6.$name            = "AOC_6";
TILE1.AOC_7.$name            = "AOC_7";
