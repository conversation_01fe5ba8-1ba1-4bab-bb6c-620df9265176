<projectSpec>
  <project
        name="aci_motor_benchmark"
        device="TMS320F280049C"
        cgtVersion="21.6.0.LTS"
        products="C2000WARE"
        outputFormat="COFF"
        launchWizard="False"
        linkerCommandFile=""
        >
    <configuration name="Signal<PERSON>hain_RAM_TMU" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=1 --define=USE_FAST_TRIG_LIB=0 -g --diag_warning=225 --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    <configuration name="SignalChain_RAM_FastRTS" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=1 --define=USE_FAST_TRIG_LIB=1 -g --diag_warning=225 --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    <configuration name="SignalChain_FLASH_FastRTS" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=1 --define=USE_FAST_TRIG_LIB=1 --define=_FLASH -g --diag_warning=225  --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    <configuration name="SignalChain_FLASH_TMU" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=1 --define=USE_FAST_TRIG_LIB=0 --define=_FLASH -g --diag_warning=225  --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    <configuration name="ControlAlgo_RAM_TMU" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=0 --define=USE_FAST_TRIG_LIB=0 -g --diag_warning=225 --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    <configuration name="SignalChain_FLASH_TMU_CLA_OFFLOAD" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=1 --define=USE_FAST_TRIG_LIB=0 --define=_FLASH --define=CLA_OFFLOAD -g --diag_warning=225 --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    <configuration name="SignalChain_RAM_TMU_CLA_OFFLOAD" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=1 --define=USE_FAST_TRIG_LIB=0 --define=CLA_OFFLOAD -g --diag_warning=225 --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    <configuration name="SignalChain_RAM_CLAmath_CLA" compilerBuildOptions="-O2 --opt_for_speed=5 -I${PROJECT_ROOT} -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_ROOT}\device_support\f28004x\headers\include -I${C2000WARE_ROOT}\libraries\math\FPUfastRTS\c28\include -v28 -ml -mt --float_support=fpu32 --idiv_support=none --tmu_support=tmu0 --vcu_support=vcu0 --fp_mode=relaxed --define=SIGNAL_CHAIN=1 --define=USE_FAST_TRIG_LIB=0 --define=CLA_CPU -g --diag_warning=225 --diag_suppress=10063 --display_error_number --abi=coffabi -k --asm_listing --src_interlist --preproc_with_compile" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x800 --define RAM" />
    
    <pathVariable name="C2000WARE_DLIB_ROOT" path="../../../../../../driverlib/f28004x/driverlib/" scope="project" />
    <pathVariable name="C2000WARE_ROOT" path="../../../../../../" scope="project" />
    <file action="copy" path="${C2000WARE_ROOT}/device_support/f28004x/common/include/driverlib.h" targetDirectory="device" />
    <file action="copy" path="${C2000WARE_ROOT}/device_support/f28004x/common/targetConfigs/TMS320F280049C_LaunchPad.ccxml" targetDirectory="targetConfigs" />
    <file action="copy" path="${C2000WARE_ROOT}/device_support/f28004x/common/source/f28004x_codestartbranch.asm" targetDirectory="device" />
    <file action="link" path="${C2000WARE_ROOT}/driverlib/f28004x/driverlib/ccs/Debug/driverlib.lib" targetDirectory="" />
    
    <file action="link" path="${C2000WARE_ROOT}/libraries/math/FPUfastRTS/c28/source/fpu32/atan2_f32.asm" targetDirectory="device" applicableConfigurations="SignalChain_RAM_FastRTS,SignalChain_FLASH_FastRTS" />
    <file action="link" path="${C2000WARE_ROOT}/libraries/math/FPUfastRTS/c28/source/fpu32/sincos_f32.asm" targetDirectory="device" applicableConfigurations="SignalChain_RAM_FastRTS,SignalChain_FLASH_FastRTS" />
    <file action="link" path="${C2000WARE_ROOT}/libraries/math/FPUfastRTS/c28/source/fpu32/FPUmathTables.asm" targetDirectory="device"  applicableConfigurations="SignalChain_RAM_FastRTS,SignalChain_FLASH_FastRTS" />
    
    <file action="link" path="${C2000WARE_ROOT}/libraries/math/CLAmath/c28/lib/cla2_math_library_datarom_fpu32.lib" targetDirectory=""  applicableConfigurations="SignalChain_RAM_CLAmath_CLA" />
    <file action="link" path="${C2000WARE_ROOT}/libraries/boot_rom/f28004x/revB/rom_symbol_libs/CLA_Data_ROM/F28004x_CLADATROM_Symbols_fpu32.lib" targetDirectory=""  applicableConfigurations="SignalChain_RAM_CLAmath_CLA" />
    <file action="copy" path="${C2000WARE_ROOT}/libraries/math/CLAmath/c28/include/CLAmath.h" targetDirectory=""  applicableConfigurations="SignalChain_RAM_CLAmath_CLA" />
    
    <file action="copy" path="../../device_support/f28004x/cmd/280049C_RAM_lnk.cmd" targetDirectory="" applicableConfigurations="SignalChain_RAM_FastRTS,SignalChain_RAM_TMU,ControlAlgo_RAM_TMU,SignalChain_RAM_TMU_CLA_OFFLOAD" />
    <file action="copy" path="../../device_support/f28004x/cmd/280049C_FLASH_lnk.cmd" targetDirectory="" applicableConfigurations="SignalChain_FLASH_FastRTS,SignalChain_FLASH_TMU,SignalChain_FLASH_TMU_CLA_OFFLOAD" />
    <file action="copy" path="../../device_support/f28004x/cmd/280049C_RAM_CLA_lnk.cmd" targetDirectory="" applicableConfigurations="SignalChain_RAM_CLAmath_CLA" />
    
    <file action="copy" path="../../device_support/f28004x/source/device.c" targetDirectory="device" />
    <file action="copy" path="../../device_support/f28004x/source/device_profile.c" targetDirectory="device" applicableConfigurations="SignalChain_RAM_FastRTS,SignalChain_RAM_TMU,ControlAlgo_RAM_TMU,SignalChain_RAM_TMU_CLA_OFFLOAD,SignalChain_FLASH_FastRTS,SignalChain_FLASH_TMU,SignalChain_FLASH_TMU_CLA_OFFLOAD"/>
    <file action="copy" path="../../device_support/f28004x/source/device_profile.cla" targetDirectory="device" applicableConfigurations="SignalChain_RAM_CLAmath_CLA" compilerBuildOptions="--cla_default"/>
    <file action="copy" path="../../device_support/f28004x/source/device_profile_init.c" targetDirectory="device" />
    <file action="copy" path="../../device_support/f28004x/include/device.h" targetDirectory="device" />
    <file action="copy" path="../../device_support/f28004x/include/device_profile.h" targetDirectory="device" />
   
    <file action="copy" path="../../common/include/IQmathLib_cla.h" targetDirectory="" applicableConfigurations="SignalChain_RAM_TMU_CLA_OFFLOAD,SignalChain_FLASH_TMU_CLA_OFFLOAD,SignalChain_RAM_CLAmath_CLA"/>
    
    <file action="copy" path="../../common/include/aci.h" targetDirectory="" />
    <file action="copy" path="../../common/include/aci_const.h" targetDirectory="" />
    <file action="copy" path="../../common/include/aci_fe.h" targetDirectory="" />
    <file action="copy" path="../../common/include/aci_fe_const.h" targetDirectory="" />
    <file action="copy" path="../../common/include/aci_main.h" targetDirectory="" />
    <file action="copy" path="../../common/include/aci_se.h" targetDirectory="" />
    <file action="copy" path="../../common/include/aci_se_const.h" targetDirectory="" />
    <file action="copy" path="../../common/include/clarke.h" targetDirectory="" />
    <file action="copy" path="../../common/include/FastFPUTrigLib.h" targetDirectory="" />
    <file action="copy" path="../../common/include/iclarke.h" targetDirectory="" />
    <file action="copy" path="../../common/include/ipark.h" targetDirectory="" />
    <file action="copy" path="../../common/include/IQmathLib.h" targetDirectory="" />
    <file action="copy" path="../../common/include/parameter.h" targetDirectory="" />
    <file action="copy" path="../../common/include/park.h" targetDirectory="" />
    <file action="copy" path="../../common/include/pid_reg3.h" targetDirectory="" />
    <file action="copy" path="../../common/include/profile.h" targetDirectory="" />
    <file action="copy" path="../../common/include/profile_print.h" targetDirectory="" />
    <file action="copy" path="../../common/include/svgen.h" targetDirectory="" />
    
    <file action="copy" path="../../common/source/aci_const.c" targetDirectory="" />
    <file action="copy" path="../../common/source/aci_fe_const.c" targetDirectory="" />
    <file action="copy" path="../../common/source/aci_main.c" targetDirectory="" />
    <file action="copy" path="../../common/source/aci_se_const.c" targetDirectory="" />
    <file action="copy" path="../../common/source/profile.c" targetDirectory="" />
    
    <file action="copy" path="../../common/source/aci_ctrl_loop_cla_offload.c" targetDirectory="" applicableConfigurations="SignalChain_RAM_TMU_CLA_OFFLOAD,SignalChain_FLASH_TMU_CLA_OFFLOAD"/>
    <file action="copy" path="../../common/source/aci_cla_offload.cla" targetDirectory="" applicableConfigurations="SignalChain_RAM_TMU_CLA_OFFLOAD,SignalChain_FLASH_TMU_CLA_OFFLOAD" />
    <file action="copy" path="../../common/source/aci_ctrl_loop.cla" targetDirectory="" applicableConfigurations="SignalChain_RAM_CLAmath_CLA" compilerBuildOptions="--cla_default --cla_signed_compare_workaround=on"/>
  </project>
</projectSpec>
