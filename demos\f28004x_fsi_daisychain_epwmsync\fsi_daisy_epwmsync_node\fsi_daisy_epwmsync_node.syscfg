/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "F28004x" --package "F28004x_100PZ" --part "F28004x_100PZ" --product "C2000WARE@**********"
 * @versions {"data":"2020052512","timestamp":"2020052512","tool":"1.5.0+1397","templates":"2020052512"}
 */

/**
 * Import the modules used in this configuration.
 */
const TILE  = scripting.addModule("/utilities/clb_tool/clb_syscfg/source/TILE");
const TILE1 = TILE.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
TILE.clock_period = 10;
TILE.sim_duration = 100000;

TILE1.$name                     = "TILE1";
TILE1.BOUNDARY.$name            = "BOUNDARY0";
TILE1.BOUNDARY.in_duty1         = 50;
TILE1.BOUNDARY.in_period1       = 100;
TILE1.BOUNDARY.in_repeat_count0 = 50;
TILE1.BOUNDARY.in_repeat_count3 = 1;
TILE1.BOUNDARY.in_repeat_count1 = 1000;
TILE1.BOUNDARY.in_duty0         = 20;
TILE1.BOUNDARY.in_period0       = 40;
TILE1.LUT_0.$name               = "LUT_0";
TILE1.LUT_1.$name               = "LUT_1";
TILE1.LUT_2.$name               = "LUT_2";
TILE1.FSM_0.$name               = "FSM_0";
TILE1.FSM_0.e0                  = "BOUNDARY.in0";
TILE1.FSM_0.e1                  = "COUNTER_0.count_match1";
TILE1.FSM_0.eqn_out             = "0";
TILE1.FSM_0.eqn_s0              = "0";
TILE1.FSM_0.eqn_s1              = "(e0 | s1) & (~e1)";
TILE1.FSM_1.$name               = "FSM_1";
TILE1.FSM_2.$name               = "FSM_2";
TILE1.COUNTER_0.$name           = "COUNTER_0";
TILE1.COUNTER_0.mode1           = "1";
TILE1.COUNTER_0.reset           = "COUNTER_0.count_match1";
TILE1.COUNTER_0.mode0           = "FSM_0.S1";
TILE1.COUNTER_0.match1_val      = "1";
TILE1.COUNTER_1.$name           = "COUNTER_1";
TILE1.COUNTER_2.$name           = "COUNTER_2";
TILE1.OUTLUT_0.$name            = "OUTLUT_0";
TILE1.OUTLUT_1.$name            = "OUTLUT_1";
TILE1.OUTLUT_2.$name            = "OUTLUT_2";
TILE1.OUTLUT_3.$name            = "OUTLUT_3";
TILE1.OUTLUT_4.$name            = "OUTLUT_4";
TILE1.OUTLUT_4.i0               = "BOUNDARY.in0";
TILE1.OUTLUT_4.eqn              = "i0";
TILE1.OUTLUT_5.$name            = "OUTLUT_5";
TILE1.OUTLUT_5.eqn              = "i0";
TILE1.OUTLUT_5.i0               = "COUNTER_0.count_match1";
TILE1.OUTLUT_6.$name            = "OUTLUT_6";
TILE1.OUTLUT_7.$name            = "OUTLUT_7";
TILE1.HLC.$name                 = "HLC_0";
TILE1.HLC.program0.$name        = "HLCP_0";
TILE1.HLC.program0.instruct0    = "mov c0,r0";
TILE1.HLC.program0.instruct1    = "add r0,r1";
TILE1.HLC.program0.instruct2    = "intr 0x7";
TILE1.HLC.program1.$name        = "HLCP_1";
TILE1.HLC.program2.$name        = "HLCP_2";
TILE1.HLC.program2.instruct0    = "intr 2";
TILE1.HLC.program2.instruct1    = "mov r0,r0";
TILE1.HLC.program3.$name        = "HLCP_3";
TILE1.AOC_0.$name               = "AOC_0";
TILE1.AOC_1.$name               = "AOC_1";
TILE1.AOC_2.$name               = "AOC_2";
TILE1.AOC_3.$name               = "AOC_3";
TILE1.AOC_4.$name               = "AOC_4";
TILE1.AOC_5.$name               = "AOC_5";
TILE1.AOC_6.$name               = "AOC_6";
TILE1.AOC_7.$name               = "AOC_7";
