<projectSpec>
  <project
        name="receiver"
        device="TMS320F280049C"
        cgtVersion="21.6.0.LTS"
        products="C2000WARE"
		outputFormat="ELF"
        launchWizard="False"
        linkerCommandFile=""
        >
	<configuration name="CPU1_RAM" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT}  -v28 -ml -mt  --float_support=fpu32 --tmu_support=tmu0 --define=CPU1 --define RAM --diag_warning=225  --diag_suppress=10063" linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x200 " />
    <configuration name="CPU1_FLASH" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT}  -v28 -ml -mt --define=_FLASH --float_support=fpu32 --tmu_support=tmu0 --define=CPU1 --diag_warning=225  --diag_suppress=10063" linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x200 " />	
    <pathVariable name="C2000WARE_DLIB_ROOT" path="../../../../../driverlib/f28004x/driverlib/" scope="project" />
    <file action="copy" path="../../../../../device_support/f28004x/common/include/driverlib.h" targetDirectory="device" />
    <file action="copy" path="../../../../../device_support/f28004x/common/include/device.h" targetDirectory="device" />
    <file action="copy" path="../../../../../device_support/f28004x/common/source/device.c" targetDirectory="device" />
    <file action="copy" path="../../../../../device_support/f28004x/common/targetConfigs/TMS320F280049C.ccxml" targetDirectory="targetConfigs" />
	<file action="copy" path="../../../../../device_support/f28004x/common/cmd/28004x_generic_ram_lnk.cmd" targetDirectory="" applicableConfigurations="CPU1_RAM" />
    <file action="copy" path="../../../../../device_support/f28004x/common/cmd/28004x_generic_flash_lnk.cmd" targetDirectory="" applicableConfigurations="CPU1_FLASH" />
	<file action="copy" path="../../../../../device_support/f28004x/common/source/f28004x_codestartbranch.asm" targetDirectory="device" />
    <file action="copy" path="../../../../../driverlib/f28004x/driverlib/" targetDirectory="device" excludeFromBuild="True" />
    <file action="link" path="../../../../../driverlib/f28004x/driverlib/ccs/Debug/driverlib.lib" targetDirectory="" />
    <file action="copy" path="receiver.c" targetDirectory="" />
  </project>
</projectSpec>
