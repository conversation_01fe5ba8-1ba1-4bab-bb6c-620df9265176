<projectSpec>
  <project
        name="fsi_daisy_epwmsync_lead"
        device="TMS320F280025C"
        cgtVersion="21.6.0.LTS"
        products="C2000WARE"
        outputFormat="ELF"
        launchWizard="False"
        enableSysConfigTool="true"
        linkerCommandFile=""
        sysConfigBuildOptions="--product ${C2000WARE_ROOT}/.metadata/sdk.json --device F28002x --package 80QFP --part F28002x_80QFP"
        >
    <configuration name="CPU1_RAM" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt --float_support=fpu32 --define=DEBUG --define RAM --diag_warning=225 --diag_suppress=10063"  linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100 "/>
    <configuration name="CPU1_FLASH" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt --float_support=fpu32 --define=DEBUG --define=_FLASH --diag_warning=225 --diag_suppress=10063"  linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100 "/>
    <configuration name="CPU1_LAUNCHXL_RAM" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt --float_support=fpu32 --define=DEBUG --define RAM --diag_warning=225 --diag_suppress=10063 --define=_LAUNCHXL_F280025C" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100  "/>
    <configuration name="CPU1_LAUNCHXL_FLASH" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -v28 -ml -mt --float_support=fpu32 --define=DEBUG --define=_FLASH --diag_warning=225 --diag_suppress=10063 --define=_LAUNCHXL_F280025C" linkerBuildOptions="--entry_point code_start --stack_size=0x200 --heap_size=0x100  "/>
    
    <pathVariable name="C2000WARE_DLIB_ROOT" path="../../../../driverlib/f28002x/driverlib/" scope="project" />
    <pathVariable name="C2000WARE_ROOT" path="../../../../" scope="project" />
    
    <file action="copy" path="../../../../device_support/f28002x/common/include/driverlib.h" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28002x/common/include/device.h" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28002x/common/source/device.c" targetDirectory="device" />
	  <file action="copy" path="../../../../device_support/f28002x/common/source/f28002x_codestartbranch.asm" targetDirectory="device" />
    
    <file action="copy" path="../../../../device_support/f28002x/common/targetConfigs/TMS320F280025C.ccxml" targetDirectory="targetConfigs"  applicableConfigurations="CPU1_RAM,CPU1_FLASH,CPU1_LAUNCHXL_RAM,CPU1_LAUNCHXL_FLASH" />
    <file action="copy" path="../../../../device_support/f28002x/common/cmd/28002x_generic_ram_lnk.cmd" targetDirectory=""  applicableConfigurations="CPU1_RAM, CPU1_LAUNCHXL_RAM" />
    <file action="copy" path="../../../../device_support/f28002x/common/cmd/28002x_generic_flash_lnk.cmd" targetDirectory=""  applicableConfigurations="CPU1_FLASH, CPU1_LAUNCHXL_FLASH" />
    <file action="link" path="../../../../driverlib/f28002x/driverlib/ccs/Debug/driverlib.lib" targetDirectory="" />
    <file action="copy" path="../../../../driverlib/f28002x/driverlib/" targetDirectory="device" excludeFromBuild="True"/>
    
    <file action="copy" path="fsi_daisy_epwmsync_lead.c" targetDirectory="" />
  </project>
</projectSpec>
