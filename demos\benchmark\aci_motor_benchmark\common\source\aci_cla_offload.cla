//#############################################################################
//
// FILE:   aci_cla_offload.cla
//
// TITLE:  ACI Motor Control Loop CLA offloaded tasks
//
//#############################################################################
//
//
// 
// C2000Ware v5.05.00.00
//
// Copyright (C) 2024 Texas Instruments Incorporated - http://www.ti.com
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//#############################################################################

//
// Include files
//

#include "svgen.h"
#include "pid_reg3.h"

extern SVGEN svgen1;
extern PIDREG3 pid1Id;

//-----------------------------------------------------------------------------
//
// Task 1 - SVGen calculation
//
// Description: Perform SVGen calculation
//
//-----------------------------------------------------------------------------

__attribute__((interrupt)) void Cla1Task1 ( void )
{
    //
    // Uncomment line to debug    
    //
    //__mdebugstop();
    
    SVGEN_calc(&svgen1);

}

//-----------------------------------------------------------------------------
//
// Task 2 - PID calculation
//
// Description: Perform PID calculation
//
//-----------------------------------------------------------------------------
__attribute__((interrupt)) void Cla1Task2 ( void )
{
    //
    // Uncomment line to debug    
    //
    //__mdebugstop();
    
    PIDREG3_calc(&pid1Id);

}

//-----------------------------------------------------------------------------
//
// Task 8 - One Time Initialization Task
//
// Description: This task will initialize the CLA global variables
//
//-----------------------------------------------------------------------------
__attribute__((interrupt))  void Cla1Task8 ( void )
{
    __mdebugstop();

}
