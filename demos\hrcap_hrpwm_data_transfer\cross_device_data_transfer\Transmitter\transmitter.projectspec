<projectSpec>
  <project
        name="transmitter"
        device="TMS320F28388D"
        cgtVersion="21.6.0.LTS"
        products="C2000WARE"
		outputFormat="ELF"
        launchWizard="False"
        linkerCommandFile=""
        >
	<configuration name="CPU1_RAM" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_SFO_INCLUDE}  -v28 -ml -mt  --float_support=fpu32 --tmu_support=tmu0 --define=CPU1 --define RAM --diag_warning=225  --diag_suppress=10063" linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x200 " />
    <configuration name="CPU1_FLASH" compilerBuildOptions="--opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} -I${C2000WARE_SFO_INCLUDE}  -v28 -ml -mt --define=_FLASH --float_support=fpu32 --tmu_support=tmu0 --define=CPU1 --diag_warning=225  --diag_suppress=10063" linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x3F8 " />	
    <pathVariable name="C2000WARE_DLIB_ROOT" path="../../../../../driverlib/f2838x/driverlib/" scope="project" />
    <pathVariable name="C2000WARE_SFO_INCLUDE" path="../../../../../libraries/calibration/hrpwm/f2838x/include/" scope="project" />
    <file action="copy" path="../../../../../device_support/f2838x/common/include/driverlib.h" targetDirectory="device" />
    <file action="copy" path="../../../../../device_support/f2838x/common/include/device.h" targetDirectory="device" />
    <file action="copy" path="../../../../../device_support/f2838x/common/source/device.c" targetDirectory="device" />
    <file action="copy" path="../../../../../device_support/f2838x/common/targetConfigs/TMS320F28388D.ccxml" targetDirectory="targetConfigs" />
	<file action="copy" path="../../../../../device_support/f2838x/common/cmd/2838x_RAM_lnk_cpu1.cmd" targetDirectory="" applicableConfigurations="CPU1_RAM" />
    <file action="copy" path="../../../../../device_support/f2838x/common/cmd/2838x_FLASH_lnk_cpu1.cmd" targetDirectory="" applicableConfigurations="CPU1_FLASH" />
	<file action="copy" path="../../../../../device_support/f2838x/common/source/f2838x_codestartbranch.asm" targetDirectory="device" />
    <file action="copy" path="../../../../../driverlib/f2838x/driverlib/" targetDirectory="device" excludeFromBuild="True" />
    <file action="link" path="../../../../../driverlib/f2838x/driverlib/ccs/Debug/driverlib.lib" targetDirectory="" />
    <file action="link" path="../../../../../libraries/calibration/hrpwm/f2838x/lib/SFO_v8_fpu_lib_build_c28_driverlib.lib" targetDirectory="" />
    <file action="copy" path="transmitter.c" targetDirectory="" />
  </project>
</projectSpec>
