//#############################################################################
//
// FILE:   device_profile.c
//
// TITLE:  Device specific profiler implementation
//
//#############################################################################
//
//
// 
// C2000Ware v5.05.00.00
//
// Copyright (C) 2024 Texas Instruments Incorporated - http://www.ti.com
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//#############################################################################

//
// Include files
//
#include "profile.h"
#include "driverlib.h"


//
// For consistent results, force the application benchmark
// functions to never be inlined
//

//
// Calibrate application benchmark to determine offset
//
#pragma FUNC_CANNOT_INLINE (Bmrk_calibrate)
void Bmrk_calibrate(void)
{
        Bmrk_start();
        Bmrk_end();
        Bmrk_Adjust = (Bmrk_Start - Bmrk_End);
        //
        // CLA adds 1 extra NOP after Bmrk_start() and 1 extra NOP before Bmrk_end().
        // Account for 2 additional cycles based on this overhead.
        //
        Bmrk_Adjust += 2;
}

//
// Save application benchmark start count
//
#pragma FUNC_CANNOT_INLINE (Bmrk_start)
void Bmrk_start(void)
{
    unsigned long  *counter = (unsigned long *) (uintptr_t)(EPWM1_BASE + EPWM_O_TBCTR);
    Bmrk_Start = *counter;
}

//
// Save application benchmark end count
//
#pragma FUNC_CANNOT_INLINE (Bmrk_end)
void Bmrk_end(void)
{
    unsigned long  *counter = (unsigned long *) (uintptr_t)(EPWM1_BASE + EPWM_O_TBCTR);
    Bmrk_End = *counter;
}

//
// Save benchmark count for marking the end
//
void IOBmrk_end(void)
{
    // Read PWM Counter
    uint16_t  *counter = (uint16_t *) (uintptr_t)(EPWM1_BASE + EPWM_O_TBCTR);
    IOBmrk_End = *counter;
}

