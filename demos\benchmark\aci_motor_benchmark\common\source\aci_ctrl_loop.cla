//#############################################################################
//
// FILE:   aci_ctrl_loop.c
//
// TITLE:  ACI Motor Control Loop (Task) on CLA
// 
// This file is a copy of the aci_ctrl_loop.c with a .cla extension for compiling
// on CLA
//
//#############################################################################
//
//
// 
// C2000Ware v5.05.00.00
//
// Copyright (C) 2024 Texas Instruments Incorporated - http://www.ti.com
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//#############################################################################

//
// Include files
//

#include "profile.h"
#include "aci_main.h"
#include "device.h"

#ifdef CLA_CPU
#include "IQmathLib_cla.h"
#endif

//
// Definitions
//
extern SVGEN svgen1;
extern PIDREG3 pid1Id;
extern PIDREG3 pid1Iq;
extern PIDREG3 pid1Spd;
extern ACI aci1;
extern ACIFE fe1;
extern ACISE se1;
extern PARK park1;
extern IPARK ipark1;
extern CLARKE clarke1;
extern ICLARKE iclarke1;

extern float idRef;
extern float speedRef;
extern float DLogCh1[];
extern float DLogCh2[];
extern uint16_t ctrlLoopCount;
extern uint32_t PWM_HALF_MAX;


//
// ACI Control Loop Implementation
//
#if SIGNAL_CHAIN == 1
interrupt
#endif
void aci_ctrlLoop(void)
{

    //
    // Uncomment to halt and debug
    //__mdebugstop();

#if SIGNAL_CHAIN == 1
    //
    // Benchmark IO response
    //
    IOBmrk_end();
    IOBmrk_calc();
    
    //
    // Read ADC and convert to float
    //
    Bmrk_calibrate();
    asm("BMRK_ADC_READ_START:");
    Bmrk_start();

    asm("ADC_READ_START:");
    _iq Ia = _IQ12toIQ(ADC_getIa());
    _iq Ib = _IQ12toIQ(ADC_getIb());
    asm("ADC_READ_END:");

    Bmrk_end();
    asm("BMRK_ADC_READ_END:");
    Bmrk_calc(BMRK_READ_ADC_CONV_FLOAT);

    //
    //  Convert the ADC measurement to phase currents
    //
    Ia = ADJUST_Ia_INPUT(Ia);
    Ib = ADJUST_Ib_INPUT(Ib);

    //
    // Perform Clarke transform
    //
    Bmrk_calibrate();
    asm("BMRK_CLARKE_CALC_START:");
    Bmrk_start();

    clarke1.As = Ia; // Phase A curr.
    clarke1.Bs = Ib; // Phase B curr.
    asm("CLARKE_CALC_START:");
    Clarke_calc(&clarke1);
    asm("CLARKE_CALC_END:");

    Bmrk_end();
    asm("BMRK_CLARKE_CALC_END:");
    Bmrk_calc(BMRK_CLARKE_TRANSFORM);

    //
    // Apply ADC derived phase current to park transform
    //
    _iq ialpha = clarke1.Alpha;
    _iq ibeta = clarke1.Beta;

#else

    //
    // Apply ACI model derived phase current to park transform
    //
    _iq ialpha = aci1.ialfa;
    _iq ibeta = aci1.ibeta;

#endif

    //
    // Perform Park transform
    //
    Bmrk_calibrate();

    asm("BMRK_PARK_CALC_START:");
    Bmrk_start();

    park1.ds = ialpha;
    park1.qs = ibeta;
    park1.ang = fe1.theta_r_fe;
    asm("PARK_CALC_START:");
    PARK_calc(&park1);
    asm("PARK_CALC_END:");

    Bmrk_end();
    asm("BMRK_PARK_CALC_END:");
    Bmrk_calc(BMRK_PARK_TRANSFORM);

    //
    // Perform PID Controller transform
    //

    Bmrk_calibrate();
    Bmrk_start();

    // Forward Control

    // PID Speed
    pid1Spd.pid_ref_reg3 = speedRef;
    pid1Spd.pid_fdb_reg3 = se1.wr_hat_se;
    asm("PID_1_CALC_START:");
    PIDREG3_calc(&pid1Spd);
    asm("PID_1_CALC_END:");

    // PID Iq
    pid1Iq.pid_ref_reg3 = pid1Spd.pid_out_reg3;
    pid1Iq.pid_fdb_reg3 = park1.qe;
    asm("PID_2_CALC_START:");
    PIDREG3_calc(&pid1Iq);
    asm("PID_2_CALC_END:");

    // PID Id
    pid1Id.pid_ref_reg3 = idRef;
    pid1Id.pid_fdb_reg3 = park1.de;
    asm("PID_3_CALC_START:");
    PIDREG3_calc(&pid1Id);
    asm("PID_3_CALC_END:");

    Bmrk_end();
    Bmrk_calc(BMRK_PID_TRANSFORM);

    //
    // Perform Inverse Park Transform
    //
    Bmrk_calibrate();
    Bmrk_start();
    ipark1.de = pid1Id.pid_out_reg3;
    ipark1.qe = pid1Iq.pid_out_reg3;
    ipark1.ang = fe1.theta_r_fe;
    asm("IPARK_CALC_START:");
    IPARK_calc(&ipark1);
    asm("IPARK_CALC_END:");
    Bmrk_end();
    Bmrk_calc(BMRK_IPARK_TRANSFORM);


    //
    // Perform ACI Motor Modeling
    //

    Bmrk_calibrate();
    Bmrk_start();
    aci1.ualfa = ipark1.ds;
    aci1.ubeta = ipark1.qs;
    asm("ACI_CALC_START:");
    ACI_calc(&aci1);
    asm("ACI_CALC_END:");
    Bmrk_end();
    Bmrk_calc(BMRK_ACI_MODEL);


    //
    // Save computed values to log buffer for plotting
    //
    DLogCh1[ctrlLoopCount] = aci1.ialfa;     // Current Plot
    DLogCh2[ctrlLoopCount] = se1.wr_hat_se;  // Speed   Plot

#if SIGNAL_CHAIN == 1
    //
    // Output modeled phase current to DAC so it can be
    // routed to ADC to complete the loop
    //

    asm("BMRK_ICLARKE_CALC_START:");
    iclarke1.Alpha = aci1.ialfa;
    iclarke1.Beta = aci1.ibeta;
    IClarke_calc(&iclarke1);
    asm("BMRK_ICLARKE_CALC_END:");

    asm("BMRK_DAC_WRITE_START:");

    //
    // Adjust the phase current values (between -1.0 abd 1.0) to a DAC value
    // that can be output and read correctly by ADC
    //

    DAC_setIa(ADJUST_Ia_OUTPUT(iclarke1.As));
    DAC_setIb(ADJUST_Ib_OUTPUT(iclarke1.Bs));

    asm("BMRK_DAC_WRITE_END:");
#endif

    //
    // Perform Flux Estimation
    //

    Bmrk_calibrate();
    Bmrk_start();
    // Feedback Control:
    fe1.u_ds_fe = ipark1.ds;
    fe1.u_qs_fe = ipark1.qs;
    fe1.i_ds_fe = aci1.ialfa;
    fe1.i_qs_fe = aci1.ibeta;
    asm("ACIFE_CALC_START:");
    ACIFE_calc(&fe1);
    asm("ACIFE_CALC_END:");
    Bmrk_end();
    Bmrk_calc(BMRK_FE_TRANSFORM);

    //
    // Perform Speed Estimation
    //
    Bmrk_calibrate();
    Bmrk_start();
    se1.i_ds_se = ipark1.ds;
    se1.i_qs_se = ipark1.qs;
    se1.psi_dr_se = fe1.psi_dr_fe;
    se1.psi_qr_se = fe1.psi_qr_fe;
    se1.theta_r_se = fe1.theta_r_fe;
    asm("ACISE_CALC_START:");
    ACISE_calc(&se1);
    asm("ACISE_CALC_END:");
    Bmrk_end();
    Bmrk_calc(BMRK_SE_TRANSFORM);


#if SIGNAL_CHAIN == 1
    //
    // Perform SVGen Transform
    //

    Bmrk_calibrate();
    asm("BMRK_SVGEN_CALC_START:");
    Bmrk_start();
    svgen1.Ualpha = ipark1.ds;
    svgen1.Ubeta  = ipark1.qs;
    asm("SVGEN_CALC_START:");
    SVGEN_calc(&svgen1);

    uint32_t Ua = _IQmpy(PWM_HALF_MAX,svgen1.Ta)+ PWM_HALF_MAX;
    uint32_t Ub = _IQmpy(PWM_HALF_MAX,svgen1.Tb)+ PWM_HALF_MAX;
    uint32_t Uc = _IQmpy(PWM_HALF_MAX,svgen1.Tc)+ PWM_HALF_MAX;
    asm("SVGEN_CALC_END:");

    Bmrk_end();
    asm("BMRK_SVGEN_CALC_END:");
    Bmrk_calc(BMRK_SVGEN);

    //
    // Write to the three PWMs. The writes simulate a real execution where
    // PWMs control the 3 phase voltage.
    //

    Bmrk_calibrate();
    asm("BMRK_PWM_WRITE_START:");
    Bmrk_start();

    asm("PWM_WRITE_START:");
    PWM_setUa(Ua);
    PWM_setUb(Ub);
    PWM_setUc(Uc);
    asm("PWM_WRITE_END:");

    Bmrk_end();
    asm("BMRK_PWM_WRITE_END:");
    Bmrk_calc(BMRK_PWM_WRITE);

#endif

    //
    // Increment control loop execution count
    //
    ++ctrlLoopCount;
    
#if SIGNAL_CHAIN == 1

    //
    // Disable interrupt if control loop execution count has
    // reached the required count
    //
#ifndef CLA_CPU  // CLA is task driven and hence interrupt disable does not apply
    if(ctrlLoopCount == DLOG_NUMBER_SAMPLES)        ADCInt_disable();
#endif

    //
    // Acknowledge the interrupt
    //
    ADCInt_ack();

#endif
   
}

