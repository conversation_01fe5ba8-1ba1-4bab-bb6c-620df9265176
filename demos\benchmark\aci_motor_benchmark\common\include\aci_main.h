//#############################################################################
//
// FILE:   aci_main.h
//
// TITLE:  ACI Motor Control Benchmark Application
//
//#############################################################################
//
//
// 
// C2000Ware v5.05.00.00
//
// Copyright (C) 2024 Texas Instruments Incorporated - http://www.ti.com
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//#############################################################################

#ifndef _ACI_MAIN_H
#define _ACI_MAIN_H

//
// Include files
//
#include "aci.h"        	//!< Include header for the ACI object
#include "aci_const.h"      //!< Include header for the ACI_CONST object
#include "aci_fe.h"        	//!< Include header for the ACIFE object
#include "aci_fe_const.h"   //!< Include header for the ACIFE_Const object
#include "aci_se.h"        	//!< Include header for the ACISE object
#include "aci_se_const.h"   //!< Include header for the ACISE_Const object
#include "park.h"       	//!< Include header for the PARK object
#include "ipark.h"       	//!< Include header for the IPARK object
#include "pid_reg3.h"       //!< Include header for the PIDREG3 object
#include "clarke.h"         //!< Include header for the CLARKE object
#include "iclarke.h"        //!< Include header for the ICLARKE object
#include "svgen.h"          //!< Include header for the SVGEN object

//
// Defines
//

#ifdef CLA_CPU
//
// CLA can only access LS and Message RAMs.
// Reducing the number of log samples saved to be able to fit application
// in CLA accessible memory.
//
#define DLOG_NUMBER_SAMPLES  800
#else
//
//
#define DLOG_NUMBER_SAMPLES  1024
#endif

#endif   //_ACI_MAIN_H
