//#############################################################################
//
// FILE:   clb_amc_failsafe_monitor_main.c
//
// TITLE:  Monitor AMC Fail-Safes with CLB Example
//
//!
//! This example demonstrates how the CLB peripheral can be used to monitor
//! the output of an AMC1306 delta-sigma modulator and detect potential
//! fault conditions.
//!
//! For more information about this implementation, refer to the accompanying
//! user guide located at:
//! C2000Ware_PATH\examples\demos\doc\Diagnosing Delta-Sigma Modulator Bitstream Using C2000 Configurable Logic Block.pdf
//!
//!
//! \b External \b Connections \n
//! - Connect GPIO0 (EPWM1A) to GPIO25 (SD_C1) on F28004x EVM and CLKIN of AMC1306EVM
//! - Connect DOUT of AMC1306EVM to GPIO24 (SD_D1) on F28004x EVM
//! - (optional) Connect ADCINA1 (DACB_OUT) to AINP of AMC1306EVM
//! - (optional) GPIO2 can be used to monitor clock within CLB
//! - (optional) GPIO3 can be used to monitor bitstream within CLB
//!
//! \b Watch \b Variables \n
//! - \b zeroBits : number of zero bits in 128-bit segment
//! - \b oneBits : number of one bits in 128-bit segment
//! - \b ratio : ratio of zero bits to one bits in 128-bit segment
//! - \b DSModulatorStatus : status indicator for delta-sigma modulator
//!                        - 0: transition status
//!                        - 1: normal operation
//!                        - 2: missing AVDD
//!                        - 3: common-mode overvoltage
//!                        - 4: negative FSR input
//!                        - 5: positive FSR input
//!
//#############################################################################
// $TI Release: $
// $Release Date: $
// 
// C2000Ware v5.05.00.00
//
// Copyright (C) 2024 Texas Instruments Incorporated - http://www.ti.com
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//#############################################################################


//
// Included Files
//
#include "driverlib.h"
#include "device.h"
#include "board.h"
#include "clb_config.h"
#include "clb.h"

//
// Defines
//
#define MAX_SAMPLES               1024U

//
// Globals
//
int16_t  filter1Result[MAX_SAMPLES];
#pragma DATA_SECTION(filter1Result, "Filter1_RegsFile");
uint32_t zeroBits;
uint32_t oneBits;
float ratio;
uint16_t DSModulatorStatus = 0;

//
// Main
//
void main(void)
{
    //
    // Initialize device clock and peripherals
    //
    Device_init();

    //
    // Board initialization
    //
    Board_init();

    //
    // CLB initialization
    //
    initTILE1(myCLB1_BASE);
    CLB_enableCLB(myCLB1_BASE);

    //
    // Initialize PIE and clear PIE registers. Disables CPU interrupts.
    //
    Interrupt_initModule();

    //
    // Initialize the PIE vector table with pointers to the shell Interrupt
    // Service Routines (ISR).
    //
    Interrupt_initVectorTable();

    //
    // Interrupts that are used in this example are re-mapped to
    // ISR functions found within this file.
    //
    Interrupt_clearACKGroup(INTERRUPT_ACK_GROUP5);
    Interrupt_register(INT_SDFM1, &INT_mySDFM1_ERR_ISR);
    Interrupt_register(INT_SDFM1DR1, &INT_mySDFM1_DR1_ISR);
    Interrupt_register(INT_CLB1, &INT_myCLB1_ISR);

    //
    // Enable interrupts required for this example
    //
    Interrupt_enable(INT_SDFM1);
    Interrupt_enable(INT_SDFM1DR1);
    Interrupt_enable(INT_CLB1);

    //
    // Enable Global Interrupt (INTM) and realtime interrupt (DBGM)
    //
    EINT;
    ERTM;

    while(1)
    {
    }
}

__interrupt void INT_mySDFM1_ERR_ISR(void)
{
    //
    // Clear SDFM flag register (SDIFLG)
    //
    SDFM_clearInterruptFlag(SDFM1_BASE, SDFM_MASTER_INTERRUPT_FLAG |
                            0xFFFF);

    //
    // Acknowledge this interrupt to receive more interrupts from group 5
    //
    Interrupt_clearACKGroup(INTERRUPT_ACK_GROUP5);
}

__interrupt void INT_mySDFM1_DR1_ISR(void)
{
    static uint16_t loopCounter1 = 0;

    SDFM_setOutputDataFormat(SDFM1_BASE, SDFM_FILTER_1, SDFM_DATA_FORMAT_16_BIT);

    //
    // Check if result buffer is full.
    //
    if(loopCounter1 >= MAX_SAMPLES)
    {
        //
        // Reset the counter. Add breakpoint at below statement to view the
        // filter results in graph view.
        //
        loopCounter1 = 0;

//        //
//        // Software breakpoint to view results.
//        // Hit run again to get updated conversions.
//        // Uncomment to halt the execution once buffer is full.
//        //
//        ESTOP0;
    }

    //
    // Read SDFM flag register (SDIFLG). Wait till the conversion is complete
    // for all the filters.
    //
    while((HWREG(SDFM1_BASE + SDFM_O_SDIFLG) & 0xF000U) != 0xF000U)
    {
    }

    //
    // Read filter result
    //
    filter1Result[loopCounter1] = (int16_t)(SDFM_getFilterData(SDFM1_BASE, SDFM_FILTER_1) >> 16U);

    //
    // Clear SDFM flag register (SDIFLG)
    //
    SDFM_clearInterruptFlag(SDFM1_BASE, SDFM_MASTER_INTERRUPT_FLAG |SDFM_FILTER_1_NEW_DATA_FLAG);

    //
    // Acknowledge this interrupt to receive more interrupts from group 5
    //
    Interrupt_clearACKGroup(INTERRUPT_ACK_GROUP5);
}

__interrupt void INT_myCLB1_ISR(void){

    //
    // Read DOUT bitstream information from CLB
    //
    oneBits = CLB_getRegister(myCLB1_BASE, CLB_REG_HLC_R0);
    zeroBits = 128 - oneBits;
    ratio = (float) oneBits / 128;

    if ((ratio > 0.03) && (ratio < 0.97)){
        //
        // No fail-safes detected by CLB
        //
        DSModulatorStatus = 1;
    }
    else if (oneBits == 0){
        //
        // Missing AVDD fail-safe
        //
        DSModulatorStatus = 2;
    }
    else if (oneBits == 128){
        //
        // Common-Mode Overvoltage fail-safe
        //
        DSModulatorStatus = 3;
    }
    else if (ratio <= 0.03){
        //
        // Negative FSR Input
        //
        DSModulatorStatus = 4;
    }
    else if (ratio >= 0.97){
        //
        // Positive FSR Input
        //
        DSModulatorStatus = 5;
    }
    else {DSModulatorStatus = 0;}

    //
    // Acknowledge this interrupt to receive more interrupts from group 5
    //
    CLB_clearInterruptTag(myCLB1_BASE);
    Interrupt_clearACKGroup(INTERRUPT_ACK_GROUP5);
}

//
// End of File
//
