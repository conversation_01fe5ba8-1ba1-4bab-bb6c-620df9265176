
MEMORY
{
PAGE 0 :
   /* BEGIN is used for the "boot to SARAM" bootloader mode   */

   BEGIN           	: origin = 0x000000, length = 0x000002
   RAMM0           	: origin = 0x0000F5, length = 0x00030B
   RAMGS0           : origin = 0x00C000, length = 0x002000
   RAMGS1      		: origin = 0x00E000, length = 0x002000
   RAMGS2      		: origin = 0x010000, length = 0x002000
   RESET           	: origin = 0x3FFFC0, length = 0x000002
   RAMLS7      	    : origin = 0x00B800, length = 0x000800

PAGE 1 :

   BOOT_RSVD       : origin = 0x000002, length = 0x0000F3     /* Part of M0, BOOT rom will use this for stack */
   RAMM1           : origin = 0x000400, length = 0x000400     /* on-chip RAM block M1 */

   RAMLS03         : origin = 0x008000, length = 0x002000
   //RAMLS0          : origin = 0x008000, length = 0x000800
   //RAMLS1          : origin = 0x008800, length = 0x000800
   //RAMLS2          : origin = 0x009000, length = 0x000800
   //RAMLS3          : origin = 0x009800, length = 0x000800
   RAMLS4      	   : origin = 0x00A000, length = 0x000800
   RAMLS5     	   : origin = 0x00A800, length = 0x000800
   RAMLS6      	   : origin = 0x00B000, length = 0x000800
   //RAMLS7      	   : origin = 0x00B800, length = 0x000800
   RAMGS3      	   : origin = 0x012000, length = 0x002000

   ADCARESULT      : origin = 0x00000B00, length = 0x00000018

   CLA1_MSGRAMLOW   : origin = 0x001480, length = 0x000080
   CLA1_MSGRAMHIGH  : origin = 0x001500, length = 0x000080
}


SECTIONS
{
   codestart        : > BEGIN,     PAGE = 0
   .TI.ramfunc      : > RAMM0      PAGE = 0
   .text            : >>RAMM0 |  RAMGS0 | RAMGS1 |  RAMGS2,   PAGE = 0
   .cinit           : > RAMM0,     PAGE = 0
   .pinit           : > RAMM0,     PAGE = 0
   .switch          : > RAMM0,     PAGE = 0
   .reset           : > RESET,     PAGE = 0, TYPE = DSECT /* not used, */

   .stack           : > RAMLS5,    PAGE = 1
   .ebss            : > RAMLS03,   PAGE = 1
   .econst          : > RAMGS3,    PAGE = 1
   .esysmem         : > RAMGS3,    PAGE = 1

   ramgs0           : > RAMGS0,    PAGE = 0
   ramgs1           : > RAMGS1,    PAGE = 0

   .cio             : > RAMLS4,    PAGE = 1
   dclfuncs         : > RAMGS2,    PAGE = 0

   AdcaResultRegsFile         : > ADCARESULT, type=NOINIT
   FPUmathTables    : > RAMLS03,   PAGE = 1

   // CLA Sections
   Cla1Prog         : > RAMLS7,           PAGE = 0
   .scratchpad      : > RAMLS6,           PAGE = 1
   .bss_cla         : > RAMLS6,           PAGE = 1
   .const_cla       : > RAMLS6,           PAGE = 1

   Cla1DataRam      : > RAMLS6,           PAGE = 1
   cla_shared       : > RAMLS6,           PAGE = 1
   Cla1ToCpuMsgRAM  : > CLA1_MSGRAMLOW,   PAGE = 1
   CpuToCla1MsgRAM  : > CLA1_MSGRAMHIGH,  PAGE = 1

}

/*
//===========================================================================
// End of file.
//===========================================================================
*/
