<projectSpec>
  <project
        name="fsi_daisy_epwmsync_lead"
        device="TMS320F280049C"
        cgtVersion="21.6.0.LTS"
        products="C2000WARE"
        outputFormat="ELF"
        launchWizard="False"
        linkerCommandFile=""
        enableSysConfigTool="true"
        sysConfigBuildOptions="--product ${C2000WARE_ROOT}/.metadata/sdk.json --device F28004x"
        >
    <configuration name="CPU1_RAM" compilerBuildOptions="-v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0 --opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} --define=DEBUG --diag_warning=225 --diag_suppress=10063 --display_error_number" linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x200 --define=RAM" />
    <configuration name="CPU1_FLASH" compilerBuildOptions="-v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0 --opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} --define=DEBUG --define=_FLASH --diag_warning=225 --diag_suppress=10063 --display_error_number" linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x200 --define=FLASH" />
    <configuration name="CPU1_LAUNCHXL_RAM" compilerBuildOptions="-v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0 --opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} --define=DEBUG --diag_warning=225 --diag_suppress=10063 --display_error_number --define=_LAUNCHXL_F280049C " linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x200 --define=RAM "/>
    <configuration name="CPU1_LAUNCHXL_FLASH" compilerBuildOptions="-v28 -ml -mt --cla_support=cla2 --float_support=fpu32 --tmu_support=tmu0 --vcu_support=vcu0 --opt_level=off -I${PROJECT_ROOT}/device -I${C2000WARE_DLIB_ROOT} --define=DEBUG --define=_FLASH --diag_warning=225 --diag_suppress=10063 --display_error_number --define=_LAUNCHXL_F280049C " linkerBuildOptions="--entry_point code_start --stack_size=0x3F8 --heap_size=0x200 --define=FLASH "/>
    <pathVariable name="C2000WARE_DLIB_ROOT" path="../../../../driverlib/f28004x/driverlib/" scope="project" />
    <pathVariable name="C2000WARE_ROOT" path="../../../../" scope="project" />
    
    <file action="copy" path="../../../../device_support/f28004x/common/include/driverlib.h" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28004x/common/include/device.h" targetDirectory="device" />
    <file action="copy" path="../../../../device_support/f28004x/common/source/device.c" targetDirectory="device" />
	  <file action="copy" path="../../../../device_support/f28004x/common/source/f28004x_codestartbranch.asm" targetDirectory="device" />
    
    <file action="copy" path="../../../../device_support/f28004x/common/targetConfigs/TMS320F280049C.ccxml" targetDirectory="targetConfigs"  applicableConfigurations="CPU1_RAM,CPU1_FLASH" />
    <file action="copy" path="../../../../device_support/f28004x/common/targetConfigs/TMS320F280049C_LaunchPad.ccxml" targetDirectory="targetConfigs" applicableConfigurations="CPU1_LAUNCHXL_RAM,CPU1_LAUNCHXL_FLASH" />
    <file action="copy" path="../../../../device_support/f28004x/common/cmd/28004x_generic_ram_lnk.cmd" targetDirectory=""  applicableConfigurations="CPU1_RAM,CPU1_LAUNCHXL_RAM" />
    <file action="copy" path="../../../../device_support/f28004x/common/cmd/28004x_generic_flash_lnk.cmd" targetDirectory=""  applicableConfigurations="CPU1_FLASH,CPU1_LAUNCHXL_FLASH" />
    <file action="link" path="../../../../driverlib/f28004x/driverlib/ccs/Debug/driverlib.lib" targetDirectory="" />
    <file action="copy" path="../../../../driverlib/f28004x/driverlib/" targetDirectory="device" excludeFromBuild="True"/>
    
    <file action="copy" path="fsi_daisy_epwmsync_lead.c" targetDirectory="" />
  </project>
</projectSpec>
